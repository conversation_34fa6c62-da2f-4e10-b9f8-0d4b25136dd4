using Microsoft.VisualStudio.TestTools.UnitTesting;
using GamesEngine.Business.Liquidity.ExternalServices;
using NBitcoin;
using System;
using System.Threading.Tasks;

namespace GamesEngineTests.Unit_Tests.Business
{
    [TestClass]
    public class NodeExplorerClientTest
    {
        private const string TEST_EXPLORER_URL = "http://localhost:30781";
        private const string TEST_RECIPIENT_ADDRESS = "tb1qw508d6qejxtdg4y5r3zarvary0c5xw7kxpjzsx";

        [TestMethod]
        public void Constructor_ValidUrl_SetsBaseAddress()
        {
            // Arrange & Act
            var client = new NodeExplorerClient(TEST_EXPLORER_URL);

            // Assert
            Assert.IsNotNull(client);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public void Constructor_NullUrl_ThrowsArgumentNullException()
        {
            // Arrange & Act & Assert
            var client = new NodeExplorerClient(null);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public void Constructor_EmptyUrl_ThrowsArgumentNullException()
        {
            // Arrange & Act & Assert
            var client = new NodeExplorerClient("");
        }

        [TestMethod]
        public void GeneratePSBTAsync_ValidParameters_DoesNotThrowOnCreation()
        {
            // Arrange
            var client = new NodeExplorerClient(TEST_EXPLORER_URL);
            var amount = Money.Satoshis(100000); // 0.001 BTC

            // Act & Assert - This test just verifies the method signature and basic validation
            // In a real test environment, you would mock the HTTP responses
            Assert.IsNotNull(client);
            
            // Test that the method exists and can be called (will fail due to network, but that's expected)
            try
            {
                var task = client.GeneratePSBTAsync(TEST_RECIPIENT_ADDRESS, amount);
                Assert.IsNotNull(task);
            }
            catch (Exception ex)
            {
                // Expected to fail in test environment due to network connectivity
                // But we're testing that the method signature is correct
                Assert.IsTrue(ex.Message.Contains("Error fetching") || ex.Message.Contains("Failed requesting"));
            }
        }

        [TestMethod]
        public void AddressBalanceAsync_ValidParameters_DoesNotThrowOnCreation()
        {
            // Arrange
            var client = new NodeExplorerClient(TEST_EXPLORER_URL);

            // Act & Assert
            Assert.IsNotNull(client);
            
            // Test that the method exists and can be called (will fail due to network, but that's expected)
            try
            {
                var task = client.AddressBalanceAsync(TEST_RECIPIENT_ADDRESS, "tBTC");
                Assert.IsNotNull(task);
            }
            catch (Exception ex)
            {
                // Expected to fail in test environment due to network connectivity
                Assert.IsTrue(ex.Message.Contains("Failed requesting") || ex.Message.Contains("Error"));
            }
        }

        [TestMethod]
        public void GetFeeRateAsync_ValidCall_DoesNotThrowOnCreation()
        {
            // Arrange
            var client = new NodeExplorerClient(TEST_EXPLORER_URL);

            // Act & Assert
            Assert.IsNotNull(client);
            
            // Test that the method exists and can be called (will fail due to network, but that's expected)
            try
            {
                var task = client.GetFeeRateAsync();
                Assert.IsNotNull(task);
            }
            catch (Exception ex)
            {
                // Expected to fail in test environment due to network connectivity
                Assert.IsTrue(ex.Message.Contains("Error fetching") || ex.Message.Contains("Failed requesting"));
            }
        }

        [TestMethod]
        public void GetUTXOsAsync_ValidDescriptor_DoesNotThrowOnCreation()
        {
            // Arrange
            var client = new NodeExplorerClient(TEST_EXPLORER_URL);
            var descriptor = "wpkh(tpubDEXo3n9A3oB4qr1cS16sJ1EWkoq2kR2aykK1pp33g2TJU1jfb3Wc8tq2fDB5DndqtjS1Bu4L5yAn2kCdp5y4yeWjGj9u7wDx1XouSQYQw5d/0/*)";

            // Act & Assert
            Assert.IsNotNull(client);
            
            // Test that the method exists and can be called (will fail due to network, but that's expected)
            try
            {
                var task = client.GetUTXOsAsync(descriptor);
                Assert.IsNotNull(task);
            }
            catch (Exception ex)
            {
                // Expected to fail in test environment due to network connectivity
                Assert.IsTrue(ex.Message.Contains("Error fetching") || ex.Message.Contains("Failed requesting"));
            }
        }

        [TestMethod]
        public void NbxUnspentCoin_Properties_WorkCorrectly()
        {
            // Arrange
            var coin = new NbxUnspentCoin
            {
                TransactionId = "0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef",
                Index = 0,
                Value = "0.001",
                ScriptPubKeyHex = "0014751e76a8e8c8c8b8c8b8c8b8c8b8c8b8c8b8c8b8",
                KeyPath = "0/1"
            };

            // Act & Assert
            Assert.IsNotNull(coin.OutPoint);
            Assert.IsNotNull(coin.Amount);
            Assert.IsNotNull(coin.ScriptPubKey);
            Assert.IsNotNull(coin.AsCoin());
            Assert.AreEqual("0/1", coin.KeyPath);
            Assert.AreEqual((uint)0, coin.Index);
        }
    }
}
