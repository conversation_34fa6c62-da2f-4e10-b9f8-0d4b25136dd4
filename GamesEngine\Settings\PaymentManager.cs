﻿using GamesEngine.Business.Liquidity.ExternalServices;
using Microsoft.Extensions.Configuration;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace GamesEngine.Settings
{
    public class PaymentManager
    {
        public static void Configure(IConfiguration configuration)
        {
            var setup = configuration.GetSection("InvoicePayment");
            if (setup == null) throw new ArgumentNullException(nameof(setup), "InvoicePayment settings section is missing in the configuration.");

            NodeExplorerUrl = setup["NodeExplorerUrl"] ?? throw new ArgumentNullException(nameof(NodeExplorerUrl), "NodeExplorerUrl is not configured.");
            if (string.IsNullOrEmpty(NodeExplorerUrl)) throw new ArgumentNullException(nameof(NodeExplorerUrl), "NodeExplorerUrl cannot be null or empty.");

            // Create HttpClient with base address for NodeExplorerClient
            var httpClient = new HttpClient();
            httpClient.BaseAddress = new Uri(NodeExplorerUrl);
            NodeExplorerClient = new NodeExplorerClient(httpClient);

            Url = setup["Url"] ?? throw new ArgumentNullException(nameof(Url), "InvoicePaymentUrl is not configured.");
            NodoUrl = setup["NodoUrl"] ?? throw new ArgumentNullException(nameof(NodoUrl), "NodoUrl is not configured.");
            NodoUser = setup["NodoUser"] ?? throw new ArgumentNullException(nameof(NodoUser), "NodoUser is not configured.");
            NodoPassword = setup["NodoPassword"] ?? throw new ArgumentNullException(nameof(NodoPassword), "NodoPassword is not configured.");
            Token = setup["Token"] ?? throw new ArgumentNullException(nameof(Token), "Token is not configured.");
            Store = setup["Store"] ?? throw new ArgumentNullException(nameof(Store), "Store is not configured.");
            WebhookSecret = setup["WebhookSecret"] ?? throw new ArgumentNullException(nameof(WebhookSecret), "WebhookSecret is not configured.");
            IssuerMail = setup["IssuerMail"] ?? throw new ArgumentNullException(nameof(IssuerMail), "IssuerMail is not configured.");
        }

        public static string NodeExplorerUrl { get; set; }
        public static NodeExplorerClient NodeExplorerClient { get; set; }

        public static string NodoUrl { get; set; }
        public static string NodoUser { get; set; }
        public static string NodoPassword { get; set; }
        public static string Url { get; private set; }
        public static string Token { get; private set; }
        public static string Store { get; private set; }
        public static string WebhookSecret { get; private set; }
        public static string IssuerMail { get; private set; }

        public static async Task<bool> ValidSignatureAsync(string payload, string signature)
        {
            if (string.IsNullOrEmpty(payload)) throw new ArgumentNullException(nameof(payload), "Payload cannot be null or empty.");
            if (string.IsNullOrEmpty(signature)) throw new ArgumentNullException(nameof(signature), "Signature cannot be null or empty.");
            if (!signature.ToString().StartsWith("sha256=")) return false; // Signature must start with "sha256="

            var providedSignature = signature.ToString().Substring("sha256=".Length);
            using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(WebhookSecret));
            var hashBytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(payload));
            var computedSignature = BitConverter.ToString(hashBytes).Replace("-", "").ToLower();

            var result = computedSignature == providedSignature.ToLower();
            return result;
        }

        public async Task<CurrencyRate> CurrencyRatesAsync(string currencyBase, string currencyRef)
        {
            if (string.IsNullOrEmpty(currencyBase)) throw new ArgumentNullException(nameof(currencyBase), "Currency base cannot be null or empty.");
            if (string.IsNullOrEmpty(currencyRef)) throw new ArgumentNullException(nameof(currencyRef), "Currency reference cannot be null or empty.");
            
            string url = $"{Url}/api/v1/stores/{Store}/rates?currencyPair={currencyBase}_{currencyRef}";

            var client = new HttpClient();
            var request = new HttpRequestMessage(HttpMethod.Get, url);
            request.Headers.Add("Authorization", $"token {Token}");

            var response = await client.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException($"Failed to retrieve currency rates: {response.ReasonPhrase}");
            }

            var content = await response.Content.ReadAsStringAsync();

            var rate = System.Text.Json.JsonSerializer.Deserialize<List<CurrencyRate>>(content).FirstOrDefault();
            return rate ?? new CurrencyRate
            {
                CurrencyPair = $"{currencyBase}_{currencyRef}",
                Rate = 0,
                Errors = new List<string> { "No rate found for the specified currency pair." }
            };
        }

        public static async Task<PaymentInvoiceResponse> InvoicePaymentMethodAsync(string invoiceId)
        {
            if (string.IsNullOrEmpty(invoiceId)) throw new ArgumentNullException(nameof(invoiceId), "Invoice ID cannot be null or empty.");

            var invoiceUrl = $"{Url}/api/v1/stores/{Store}/invoices/{invoiceId}/payment-methods";

            var httpClient = new HttpClient();
            var request = new HttpRequestMessage(HttpMethod.Get, invoiceUrl);
            request.Headers.Add("Authorization", $"token {Token}");

            var response = await httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException($"Failed to retrieve payment methods for invoice {invoiceId}: {response.ReasonPhrase}");
            }
            var responseContent = await response.Content.ReadAsStringAsync();
            var options = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
            // Para deserializar un array de PaymentInvoiceResponse:
            var paymentInvoiceResponses = System.Text.Json.JsonSerializer.Deserialize<List<PaymentInvoiceResponse>>(responseContent, options);
            if (paymentInvoiceResponses == null) throw new InvalidOperationException("Failed to deserialize invoice response.");

            if (paymentInvoiceResponses.Count == 0) throw new InvalidOperationException($"No payment methods found for invoice {invoiceId}.");
            return paymentInvoiceResponses.FirstOrDefault();
        }

        public static async Task<PaymentMethodBalanceResponse> PaymentMethodBalanceAsync(string paymentMethodCurrency)
        {
            if (string.IsNullOrEmpty(paymentMethodCurrency)) throw new ArgumentNullException(nameof(paymentMethodCurrency), "Payment method currency cannot be null or empty.");
            var balanceUrl = $"{Url}/api/v1/stores/{Store}/payment-methods/{paymentMethodCurrency}-ONCHAIN/wallet";
            var httpClient = new HttpClient();
            var request = new HttpRequestMessage(HttpMethod.Get, balanceUrl);

            request.Headers.Add("Authorization", $"token {Token}");

            var response = await httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException($"Failed to retrieve balance for payment method {paymentMethodCurrency}-ONCHAIN: {response.ReasonPhrase}");
            }
            var responseContent = await response.Content.ReadAsStringAsync();
            var options = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
            var balanceResponse = System.Text.Json.JsonSerializer.Deserialize<PaymentMethodBalanceResponse>(responseContent, options);
            if (balanceResponse == null) throw new InvalidOperationException("Failed to deserialize balance response.");
            return balanceResponse;
        }

        public class PaymentMethodBalanceResponse
        {
            public string Balance { get; set; }
            public string UnconfirmedBalance { get; set; }
            public string ConfirmedBalance { get; set; }
            public string Label { get; set; }
        }

        public static async Task<InvoiceResponse> CreateInvoiceAsync(string currency, decimal amount, int orderId, string orderDesc)
        {
            var invoiceUrl = $"{Url}/api/v1/stores/{Store}/invoices";

            var httpClient = new HttpClient();
            var request = new HttpRequestMessage(HttpMethod.Post, invoiceUrl);
            request.Headers.Add("Authorization", $"Bearer {Token}");

            var invoiceContent = System.Text.Json.JsonSerializer.Serialize(new
            {
                amount,
                currency,
                metadata = new
                {
                    IssuerMail,
                    orderId,
                    orderDesc
                },
                checkout = new
                {
                    speedPolicy = "HighSpeed"
                }
            });

            request.Content = new StringContent(invoiceContent, Encoding.UTF8, "application/json");
            var response = await httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException($"Failed to create invoice: {response.ReasonPhrase}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var options = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
            var invoiceResponse = System.Text.Json.JsonSerializer.Deserialize<InvoiceResponse>(responseContent, options);
            if (invoiceResponse == null)
            {
                throw new InvalidOperationException("Failed to deserialize invoice response.");
            }

            return invoiceResponse;
        }

        internal static async Task<int> TotalConfirmatios2Async(string paymentId)
        {
            int result = 0;
            return result;
        }

        internal static async Task<int> TotalConfirmatiosAsync(Invoice invoice)
        {
            if (invoice == null) throw new ArgumentNullException(nameof(invoice));
            int slipIndex = invoice.PaymentId.IndexOf('-');
            string paymentId = invoice.PaymentId.Substring(0, slipIndex);
            if (string.IsNullOrEmpty(paymentId)) throw new ArgumentNullException(nameof(paymentId));

            return await TotalConfirmatiosAsync(paymentId);
        }

        internal static async Task<int> TotalConfirmatiosAsync(string paymentId)
        {
            if (string.IsNullOrEmpty(paymentId)) throw new ArgumentNullException(nameof(paymentId));

            var nodoUrl = $"{NodoUrl}/";
            var httpClient = new HttpClient();
            
            //var byteArray = Encoding.ASCII.GetBytes("bitcoin:123456");
            var byteArray = Encoding.ASCII.GetBytes($"{NodoUser}:{NodoPassword}");
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(byteArray));
            
            var request = new HttpRequestMessage(HttpMethod.Post, nodoUrl);
            var jsonBody = $@"{{
                ""jsonrpc"": ""1.0"",
                ""id"": ""akai-check"",
                ""method"": ""getrawtransaction"",
                ""params"": [""{paymentId}"", true]
            }}";

            var content = new StringContent(jsonBody, Encoding.UTF8, "text/plain");
            var response = await httpClient.PostAsync(nodoUrl, content);
            if (!response.IsSuccessStatusCode)
            {
                throw new GameEngineException($"Failed to get transaction details: {response.ReasonPhrase} in {nodoUrl}");
            }
            var responseContent = await response.Content.ReadAsStringAsync();
            var options = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
            var transactionDetails = System.Text.Json.JsonSerializer.Deserialize<RawTransactionResponse>(responseContent, options);
            if (transactionDetails == null)
            {
                throw new GameEngineException("Failed to deserialize transaction details or confirmations are missing.");
            }
            var result = transactionDetails.Result.Confirmations;
            return result;
        }

        public class ExchangeRateResponse
        {
            public string CryptoCode { get; set; }
            public decimal Rate { get; set; }
        }

        internal static async Task<ValidAddressResponse> ValidAddressAsync(string address)
        {
            if (string.IsNullOrEmpty(address)) throw new ArgumentNullException(nameof(address));

            var nodoUrl = $"{NodoUrl}/";
            var httpClient = new HttpClient();

            var byteArray = Encoding.ASCII.GetBytes($"{NodoUser}:{NodoPassword}");
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(byteArray));

            var request = new HttpRequestMessage(HttpMethod.Post, nodoUrl);
            var jsonBody = $@"{{
                ""jsonrpc"": ""1.0"",
                ""id"": ""validate_test"",
                ""method"": ""validateaddress"",
                ""params"": [""{address}""]
            }}";

            var content = new StringContent(jsonBody, Encoding.UTF8, "application/json");
            var response = await httpClient.PostAsync(nodoUrl, content);
            if (!response.IsSuccessStatusCode)
            {
                throw new GameEngineException($"Failed to check valid address: {response.ReasonPhrase} in {nodoUrl}");
            }
            var responseContent = await response.Content.ReadAsStringAsync();
            var options = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
            var transactionDetails = System.Text.Json.JsonSerializer.Deserialize<ValidAddressResponse>(responseContent, options);
            if (transactionDetails == null)
            {
                throw new GameEngineException("Failed to deserialize transaction details or confirmations are missing.");
            }
            return transactionDetails;
        }

        public class ValidAddressResponse
        {
            public ValidAddressResult Result { get; set; }
            public string Error { get; set; }
            public string Id { get; set; }

            public class ValidAddressResult
            {
                public bool Isvalid { get; set; }
                public string Address { get; set; }
                public string ScriptPubKey { get; set; }
                public bool Isscript { get; set; }
                public bool Iswitness { get; set; }
                public int Witness_version { get; set; }
                public string Witness_program { get; set; }
            }
        }

        public static async Task<ExchangeRateResponse> ExchangeRateAsync(string kind, string referenceKind)
        {
            if (string.IsNullOrEmpty(kind)) throw new ArgumentNullException(nameof(kind), "Kind cannot be null or empty.");
            if (string.IsNullOrEmpty(referenceKind)) throw new ArgumentNullException(nameof(referenceKind), "Reference kind cannot be null or empty.");
            
            var url = $"{Url}/api/rates?storeId={Store}&currencyPairs={kind}_{referenceKind}";
            using var httpClient = new HttpClient();
            var request = new HttpRequestMessage(HttpMethod.Get, url);
            request.Headers.Add("Authorization", $"token {Token}");
            var response = await httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException($"Failed to retrieve exchange rate: {response.ReasonPhrase}");
            }
            var content = await response.Content.ReadAsStringAsync();
            var options = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
            var rates = System.Text.Json.JsonSerializer.Deserialize<List<ExchangeRateResponse>>(content, options);
            if (rates == null || !rates.Any())
            {
                throw new Exception($"No exchange rate found for {kind} to {referenceKind}.");
            }
            var rate = rates.FirstOrDefault();
            if (rate == null) throw new Exception($"Failed to retrieve exchange rate for {kind} to {referenceKind}.");
            return rate;
        }

        public class DestinationAmount
        {
            public string Destination { get; set; }
            public decimal Amount { get; set; }
        }

        internal static async Task<TransactionResponse> RequestWithdrawalsAsync(IEnumerable<DestinationAmount> destinations, decimal feeRate)
        {
            if (destinations == null || !destinations.Any()) throw new ArgumentNullException(nameof(destinations), "Destinations cannot be null or empty.");
            if (feeRate < 0) throw new ArgumentOutOfRangeException(nameof(feeRate), "Fee rate cannot be negative.");
            
            var url = $"{Url}/api/v1/stores/{Store}/payment-methods/onchain/btc/wallet/transactions";

            using var httpClient = new HttpClient();
            var request = new HttpRequestMessage(HttpMethod.Post, url);
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", Token);
            var requestBody = new
            {
                destinations = destinations.Select(d => new { destination = d.Destination, amount = d.Amount.ToString(CultureInfo.InvariantCulture) }),
                feeRate,
                allowRBF = true
            };
            request.Content = new StringContent(JsonSerializer.Serialize(requestBody), Encoding.UTF8, "application/json");
            var response = await httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                throw new Exception($"Failed to request withdrawals: {response.ReasonPhrase}. Error: {errorContent}");
            }
            var responseContent = await response.Content.ReadAsStringAsync();
            var options = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
            var transactionResponse = System.Text.Json.JsonSerializer.Deserialize<TransactionResponse>(responseContent, options);
            if (transactionResponse == null) throw new Exception("Failed to deserialize transaction response.");
            return transactionResponse;
        }

        internal static async Task ArchivePullPaymentAsync(string pullPaymentId)
        {
            if (string.IsNullOrEmpty(pullPaymentId)) throw new ArgumentNullException(nameof(pullPaymentId), "Pull payment ID cannot be null or empty.");

            var url = $"{Url}/api/v1/stores/{Store}/pull-payments/{pullPaymentId}";

            using (var httpClient = new HttpClient())
            {
                var request = new HttpRequestMessage(HttpMethod.Delete, url);
                request.Headers.Add("Authorization", $"Bearer {Token}");

                var response = await httpClient.SendAsync(request);
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    throw new Exception($"Failed to archive pull payment: {response.ReasonPhrase}. Error: {errorContent}");
                }
            }
        }

        private static RestClient _postUpdateWagersClient;
        internal static void ArchivePullPayment(string pullPaymentId)
        {
            if (string.IsNullOrEmpty(pullPaymentId)) throw new ArgumentNullException(nameof(pullPaymentId), "Pull payment ID cannot be null or empty.");

            var url = $"{Url}/api/v1/stores/{Store}/pull-payments/{pullPaymentId}";

            var request = new RestRequest(url, Method.Delete);
            request.AddHeader("Authorization", $"Bearer {Token}");            

            if (_postUpdateWagersClient == null) _postUpdateWagersClient = new RestClient();
            var response = _postUpdateWagersClient.Execute(request);

            if (response.StatusCode != HttpStatusCode.OK)
            {
                throw new Exception("Failed to archive pull payment: " + response.Content);
            }
        }

        internal static async Task<PullPaymentResponse> CreatePullPaymentAsync(string name, decimal amount, string kind)
        {
            if (string.IsNullOrEmpty(name)) throw new ArgumentNullException(nameof(name), "Name cannot be null or empty.");
            if (amount <= 0) throw new ArgumentOutOfRangeException(nameof(amount), "Amount must be greater than zero.");
            if (string.IsNullOrEmpty(kind)) throw new ArgumentNullException(nameof(kind), "Kind cannot be null or empty.");

            var url = $"{Url}/api/v1/stores/{Store}/pull-payments";

            using var httpClient = new HttpClient();
            var request = new HttpRequestMessage(HttpMethod.Post, url);
            request.Headers.Add("Authorization", $"Bearer {Token}");
            var requestBody = new
            {
                name,
                amount = amount.ToString(CultureInfo.InvariantCulture),
                //currency,
                currency = kind,
                autoApproveClaims = true,
                paymentMethods = new List<string> { $"{kind}-CHAIN" }
            };

            request.Content = new StringContent(JsonSerializer.Serialize(requestBody), Encoding.UTF8, "application/json");
            var response = await httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                throw new Exception($"Failed to create pull payment: {response.ReasonPhrase}. Error: {errorContent}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var options = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
            var pullPaymentResponse = System.Text.Json.JsonSerializer.Deserialize<PullPaymentResponse>(responseContent, options);
            if (pullPaymentResponse == null) throw new Exception("Failed to deserialize pull payment response.");

            return pullPaymentResponse;
        }

        internal static async Task<ClaimPullPaymentResponse> ClaimPullPaymentAsync(string pullPaymentId, string destination, decimal amount, string kind)
        {
            if (string.IsNullOrEmpty(pullPaymentId)) throw new ArgumentNullException(nameof(pullPaymentId), "Pull payment ID cannot be null or empty.");
            if (string.IsNullOrEmpty(destination)) throw new ArgumentNullException(nameof(destination), "Destination cannot be null or empty.");
            if (amount <= 0) throw new ArgumentOutOfRangeException(nameof(amount), "Amount must be greater than zero.");
            if (string.IsNullOrEmpty(kind)) throw new ArgumentNullException(nameof(kind), "Kind cannot be null or empty.");

            var url = $"{Url}/api/v1/pull-payments/{pullPaymentId}/payouts";
            using var httpClient = new HttpClient();
            var request = new HttpRequestMessage(HttpMethod.Post, url);
            request.Headers.Add("Authorization", $"Bearer {Token}");

            var requestBody = new
            {
                destination,
                amount = amount.ToString(CultureInfo.InvariantCulture),
                payoutMethodId = $"{kind}-CHAIN",
            };

            request.Content = new StringContent(JsonSerializer.Serialize(requestBody), Encoding.UTF8, "application/json");
            var response = await httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                throw new Exception($"Failed to claim pull payment: {response.ReasonPhrase}. Error: {errorContent}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var options = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
            var claimPullPaymentResponse = System.Text.Json.JsonSerializer.Deserialize<ClaimPullPaymentResponse>(responseContent, options);
            if (claimPullPaymentResponse == null) throw new Exception("Failed to deserialize claim pull payment response.");

            return claimPullPaymentResponse;
        }

        public class ClaimPullPaymentResponse
        {
            public string Id { get; set; }
        }

        public class PullPaymentResponse
        {
            public string Id { get; set; }
        }

        public class TransactionResponse
        {
            public string TransactionHash { get; set; }
            public decimal Amount { get; set; }
        }

        public class InvoiceResponse
        {
            public string Id { get; set; }
        }
        public class PaymentInvoiceResponse
        {
            public string Destination { get; set; }
            public string PaymentLink { get; set; }
            public string Rate { get; set; }
            public string Amount { get; set; }
            public string TotalPaid { get; set; }
            public string Due { get; set; }
            public List<Payment> Payments { get; set; }
            public class Payment
            {
                public string Id { get; set; }
                public long ReceivedDate { get; set; }
                public string Value { get; set; }
                public string Fee { get; set; }
                public string Status { get; set; }
                public string Destination { get; set; }
            }
        }

        public class CurrencyRate
        {
            public string CurrencyPair { get; set; }
            public decimal Rate { get; set; }
            public List<string> Errors { get; set; }
        }

        internal class RawTransactionResponse
        {
            public RawTransactionResult Result { get; set; }
            public object Error { get; set; }
            public string Id { get; set; }
        }

        internal class RawTransactionResult
        {
            public string Txid { get; set; }
            public string Hash { get; set; }
            public int Version { get; set; }
            public int Size { get; set; }
            public int Vsize { get; set; }
            public int Weight { get; set; }
            public int Locktime { get; set; }
            public List<RawTransactionVin> Vin { get; set; }
            public List<RawTransactionVout> Vout { get; set; }
            public string Hex { get; set; }
            public string Blockhash { get; set; }
            public int Confirmations { get; set; }
            public long Time { get; set; }
            public long Blocktime { get; set; }
        }

        internal class RawTransactionVin
        {
            public string Txid { get; set; }
            public int Vout { get; set; }
            public RawTransactionScriptSig ScriptSig { get; set; }
            public List<string> Txinwitness { get; set; }
            public long Sequence { get; set; }
        }

        internal class RawTransactionScriptSig
        {
            public string Asm { get; set; }
            public string Hex { get; set; }
        }

        internal class RawTransactionVout
        {
            public decimal Value { get; set; }
            public int N { get; set; }
            public RawTransactionScriptPubKey ScriptPubKey { get; set; }
        }

        public class RawTransactionScriptPubKey
        {
            public string Asm { get; set; }
            public string Desc { get; set; }
            public string Hex { get; set; }
            public string Address { get; set; }
            public string Type { get; set; }
        }

        public class Invoice
        {
            internal string Id { get; private set; }

            internal string PaymentId { get; private set; }

            internal decimal Due { get; private set; }
            internal decimal TotalPaid { get; private set; }
            internal decimal Rate { get; private set; }

            internal string InvoceStoreId { get; private set; }

            internal Invoice(string id, decimal invoiceDue, decimal totalPaid, decimal rate, string paymentId, string invoceStoreId)
            {
                if (string.IsNullOrWhiteSpace(id)) throw new ArgumentException("Invoice ID cannot be null or empty.", nameof(id));
                if (string.IsNullOrWhiteSpace(paymentId)) throw new ArgumentException("Payment ID cannot be null or empty.", nameof(paymentId));
                if (totalPaid < 0) throw new ArgumentOutOfRangeException(nameof(totalPaid), "Total paid cannot be negative.");
                if (rate < 0) throw new ArgumentOutOfRangeException(nameof(rate), "Rate cannot be negative.");
                if (string.IsNullOrEmpty(invoceStoreId)) throw new ArgumentException("Invoice store ID cannot be null or empty.", nameof(invoceStoreId));

                Id = id;
                Due = invoiceDue;
                TotalPaid = totalPaid;
                Rate = rate;
                PaymentId = paymentId;
                InvoceStoreId = invoceStoreId;
            }

            public enum DueStatus
            {
                Paid,
                Overpaid,
                Underpaid,
                NotSet
            }

            internal DueStatus Status
            {
                get
                {
                    if (Due == 0)
                    {
                        return DueStatus.Paid;
                    }
                    else if (Due < 0)
                    {
                        return DueStatus.Overpaid;
                    }
                    else if (Due > 0)
                    {
                        return DueStatus.Underpaid;
                    }
                    else
                    {
                        return DueStatus.NotSet;
                    }
                }
            }
        }
    }
}
